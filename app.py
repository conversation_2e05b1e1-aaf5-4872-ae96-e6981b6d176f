from io import BytesIO
import os
import re
import urllib.parse

from flask import Flask, request, render_template, Response, send_file
from openpyxl import load_workbook
import requests

from maps_helper import ImageCache


app = Flask(__name__)

XLSX_COLUMNS = {
    "name": 2,
    "company": 2,
    "telephone": 3,
    "address": 4,
    "category": 8,
    "keyword": 9,
    "website": 10,
    "maps": 11,
    "lat": 12,
    "lon": 13,
    "region": 16,
    "lead": 15,
}
ADDRESS_PATTERN = re.compile(r"(.+), (\d{5}) (.+)")
# TODO if pattern fails mark for future manual review
maps_api = ImageCache()


@app.get("/")
def landing_page():
    # initial upload page
    return render_template("upload.html")


@app.post("/upload")
def upload_xlsx():
    # check if a file was uploaded
    if "file" not in request.files or not (file := request.files["file"]):
        return render_template("upload.html", error="No file was selected for upload")

    try:
        # parse xlsx file
        wb = load_workbook(filename=BytesIO(file.stream.read()))
        ws = wb.active
        ws.insert_cols(15,4)
        data = ws_to_dict(ws)

        # next steps:
        # - add options to upload.html (e.g. delete duplicates)
        # - further parse data from xlsx -> also clean up data
        # - also consider keywords
        # - maybe find a way to determine region from zipcode
        #    idea: LUT for regions from zipcodes
        # - process lovoo results
        return render_template("captcha.html", location_data=data)
    # catch all errors, display message on upload page
    except Exception as e:
        print(e)
        return render_template(
            "upload.html",
            error="An unknown error has occured. Check the server logs or contact an administrator"
        )


@app.post("/tinder")
def tinder():
    data = list(filter(lambda x: x["selected"], request.json))
    if not data:
        return render_template("empty.html")
    # data has been filtered
    return render_template("tinder.html", location_data=data)


@app.post("/tinder_inner")
def tinder_inner():
    return render_template("tinder_inner.html", single_location_data=request.json)


@app.post("/final")
def final_page():
    data = list(filter(lambda x: x["selected"], request.json))
    if not data:
        return render_template("empty.html")
    return render_template("final.html", location_data=data)


@app.route("/maps_image")
def maps_image():
    lat = request.args.get("lat", 0)
    lon = request.args.get("lon", 0)
    return send_file(maps_api.get_image((lat, lon), marker="marker" in request.args))


def ws_to_dict(worksheet):
    entries = [
        {
            key: row[idx].value for key, idx in XLSX_COLUMNS.items()
        } for row in list(worksheet.rows)[1:-1]
    ]
    for row in entries:
        # split address into postal code, street name, street number
        # if no address is given (None), these values will also be None
        match_groups = [None] * 3
        try:
            match = re.match(ADDRESS_PATTERN, row["address"])
            match_groups = match.groups()
        except (TypeError, AttributeError):
            pass
        finally:
            row["street_number"], row["zip"], row["city"] = match_groups
        row["name"] = sanitize_cell(row["name"])
        row["address"] = sanitize_cell(row["address"])
        if row["website"] is None:
            row["website"] = ""
        if row["category"]:
            row["category"] = sanitize_cell(row["category"])
        else:
            row["category"] = "none"
        row["company"] = sanitize_cell(row["company"])
        row["street_number"] = sanitize_cell(row["street_number"])
        row["maps"] = ""
        if isinstance(row["city"], str):
            row["city"] = sanitize_cell(row["city"].split(",")[0])
            row["maps"] = "https://www.google.com/maps?q=" + urllib.parse.quote(row["company"] + " " + row["street_number"] + ", " + row["zip"] + " " + row["city"]) + "&t=k"
        row["zip"] = sanitize_cell(row["zip"])
        row["lon"] = row["lon"].split("!")[0]
        row["lead"] = "vorqualifiziert"
        row["quality"] = "normal"
        row["region"] = ""
        if row["telephone"] is None:
            row["telephone"] = ""
        if row["city"] is None:
            row["city"] = ""

    # TODO Needs more testing, non-duplicates get removed as well
    # entries = [dict(t) for t in {tuple(d.items()) for d in entries}]
    return entries

def sanitize_cell(cell):
    if isinstance(cell, str):
        # Escape comma with escaped quotes to not mess up CSV output
        return cell.replace(",", ";")
    else:
        return ""

@app.get("/maps_api.js")
def maps_api_js():
    request_url = "?key=KEYKEYKEY&callback=initMap&&v=weekly"
    js = requests.get("https://maps.googleapis.com/maps/api/js", params={
        "key": "AIzaSyAM8leq9UAKB7R-_hR2QZnUDOsjG92eQWI",
        # "callback": "initMap",
        "libraries": "",
        "v": "weekly"
    }).text
    return Response(
        js,
        mimetype="application/javascript",
        headers={
            "Content-disposition": "attachment; filename=maps_api.js"
        }
    )


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000, debug=True)
