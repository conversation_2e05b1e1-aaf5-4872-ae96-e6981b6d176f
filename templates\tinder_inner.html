<script>
    single_location_data = {{ single_location_data | tojson | safe }};

    function initMap() {
        var myLatLng = new google.maps.LatLng(single_location_data.lat, single_location_data.lon);

        var mapOptions = {
            zoom: 18,
            center: myLatLng,
            mapTypeId: 'hybrid'
        }

        var marker = new google.maps.Marker({
            position: myLatLng,
            title: single_location_data.name
        });

        map = new google.maps.Map(document.getElementById("map"),mapOptions);
        marker.setMap(map);
    }
    initMap();

</script>
<div class="row justify-content-md-center">
    <div class="col-md-auto" style="max-width : none;">
        <!--<img src="/maps_image?lat={{ single_location_data.lat }}&lon={{ single_location_data.lon }}&marker=1">-->
        <div id="map" style="height:500px;width:500px;"></div>
    </div>
    <div class="col-md-auto">
        <h5>{{ single_location_data.company }}</h5>
        <!-- TODO display more info and beautify text block
                  * we have way more space available, we should use it
                  * maybe steal some design ideas from discord embeds
        -->
        <div class="row">
            <div class="col">Website-Url</div>
            <div class="col"><a target="_blank" href={{ single_location_data.website }}>{{ single_location_data.website }}</a></div>
        </div>
        <div class="row">
            <div class="col">Adresse</div>
            <div class="col">{{ single_location_data.address }}</div>
        </div>
        <div class="row">
            <div class="col">Telephone</div>
            <div class="col">{{ single_location_data.telephone }}</div>
        </div>
    </div>
    <!-- spacer column -->
    <div class="col-md-auto"></div>
</div>