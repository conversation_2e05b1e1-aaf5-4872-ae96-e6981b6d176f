import os
import shutil
import time

import requests


class ImageCache:
    # TODO Add "empty cache" function
    CACHE_PATH = "./image_cache"

    def __init__(self):
        try:
            os.makedirs(self.CACHE_PATH)
        except FileExistsError:
            pass
        self.API_KEY = "AIzaSyAM8leq9UAKB7R-_hR2QZnUDOsjG92eQWI"

    def get_image(self, position, marker=True):
        # check whether the image is cached locally
        path = self._params_to_path(position, marker)
        if not os.path.exists(path):
            # get from maps api
            self._get_api_image(position, marker)
        if not os.path.exists(path):
            # error: return dummy image
            return "static/maps_error.png"
        # get from cache
        return path

    def _get_api_image(self, position, marker):
        # get image
        print(f"requesting maps api for {position}, marker={marker}")
        target_path = self._params_to_path(position, marker)
        pos_str = ",".join(map(str, position))
        r = requests.get(
            "https://maps.googleapis.com/maps/api/staticmap",
            #TODO make params adjustable on frontpage
            params={
                "key": self.API_KEY,
                "center": pos_str,
                # 20 = Close range single housing, 19-18 larger buildings
                "zoom": 18,
                # NOTE For testing switched to hybrid for labels
                "maptype": "hybrid",
                # TODO set dimension based on device
                "size": "500x500" if marker else "500x500",
                "markers": f"label:A|{pos_str}" if marker else None
            },
            stream=True
        )
        if r.status_code == 200:
            r.raw.decode_content = True
            print(r.raw)
            with open(target_path, "wb") as f:
                shutil.copyfileobj(r.raw, f)
        # TODO smarter request limiting
        time.sleep(0.2)

    def _params_to_path(self, position, marker):
        return self.CACHE_PATH + "/" + "_".join(map(
            lambda x: str(x).lower(),
            ("img",) + position + (marker,)
        )) + ".png"
