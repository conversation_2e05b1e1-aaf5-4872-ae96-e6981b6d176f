{% extends "base.html" %}
{% block head %}
<script type="application/javascript">
    function convertToCSV(objArray) {
        var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
        var str = '';

        for (var i = 0; i < array.length; i++) {
            var line = '';
            for (var index in array[i]) {
                if((index === "selected") == false && (index === "address") == false){
                    if (line != '') line += ','
                    line += array[i][index];
                    }
            }
            str += line + '\r\n';
        }
        return str;
    }

    location_data = {{ location_data | tojson | safe }};

    //Adds Header
    location_data.unshift(
    {
    "category": "Maps Crawler Klassifikation",
    "city": "Stadt",
    "company": "Firma",
    "keyword": "Maps Crawler Search Keyword",
    "lat": "latitude",
    "lead": "Lead-Status",
    "lon": "longitude",
    "maps": "Google Maps Link",
    "name": "Nachname",
    "quality": "Quality",
    "region": "Region",
    "street_number": "Straße",
    "telephone": "Tel.",
    "website": "Webseite",
    "zip": "Postleitzahl"
});

    var jsonObject = JSON.stringify(location_data);
    var csv = this.convertToCSV(jsonObject);
    var exportedFilename = 'output.csv' || 'export.csv';

    var blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    if (navigator.msSaveBlob) { // I hate IE 10+
        navigator.msSaveBlob(blob, exportedFilename);
    } else {
        var link = document.createElement("a");
        if (link.download !== undefined) { // feature detection
            // Browsers that support HTML5 download attribute
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", exportedFilename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
</script>
{% endblock %}
{% block content %}
<h1>Export-Übersicht</h1>
data: {{ location_data }}
<!-- TODO create a final list overview
          * maybe also allow data editing for missing fields
          * currently, we just add download button to the tinder page. this is sufficient for now.
-->
{% endblock %}