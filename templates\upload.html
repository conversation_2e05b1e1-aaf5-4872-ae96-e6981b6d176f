{% extends "base.html" %}
{% block content %}
<script type="application/javascript">
    // declare variables that will be used on multiple pages for consistency
    let current_index;
    let location_data;
    let map;
</script>
<div class="container">
    <h1>Excel-Upload</h1>
    {% if error %}
    <div class="alert alert-warning alert-dismissible show fade" role="alert">
        <strong>Uh-oh! :(</strong><br>
        {{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endif %}
    <row> <!-- this should be <div class="row">, however <row> works too (for some unknown reason) -->
        <form action="/upload"
              method="post"
              class="dropzone"
              id="my-awesome-dropzone">

        </form>
    </row>
    <div class="container">
        <div class="row">
            <button type="button" class="btn btn-primary" id="btn_upload">Upload</button>
        </div>
    </div>
    <script type="text/javascript"
            src="{{ url_for('static', filename='dropzone_config.js') }}"></script>
</div>
{% endblock %}