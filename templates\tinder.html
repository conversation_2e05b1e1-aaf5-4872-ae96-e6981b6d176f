{% extends "base.html" %}
{% block head %}
<script src="/maps_api.js" async></script>
<script type="application/javascript">
    location_data = {{ location_data | tojson | safe }};
    current_index = 0;
    $("#progress").text(1 + " / " + location_data.length);
    $(document).ready(function () {
        console.log(location_data.length);
        load_inner_container();
        $("#yay").click(function () {
            location_data[current_index].selected = true;
            location_data[current_index].quality = "normal";
            current_index++;
            $("#back").prop("disabled", false);
            load_inner_container();
        });
        $("#nay").click(function () {
            location_data[current_index].selected = false;
            location_data[current_index].quality = "normal";
            current_index++;
            $("#back").prop("disabled", false);
            load_inner_container();
        });
        $("#premium").click(function () {
            location_data[current_index].selected = true;
            location_data[current_index].quality = "premium";
            current_index++;
            $("#back").prop("disabled", false);
            load_inner_container();
        });
        $("#back").click(function (){
            current_index--;
            if(current_index === 0){
                $(this).prop("disabled", true);
            }
            load_inner_container();
        });
    });

    document.addEventListener('keydown', function(event) {
        if (event.keyCode == 37) {
            //alert('Left was pressed');
            $("#nay").click();
        }
        else if (event.keyCode == 39) {
            //alert('Right was pressed');
            $("#yay").click();
        }
    }, true);

    function load_inner_container() {
        if (current_index < location_data.length) {
            // more locations available -> load next
            fetch("/tinder_inner", {
                method: "POST",
                headers: {"Content-Type": "application/json"},
                body: JSON.stringify(location_data[current_index])
            }).then(function (response) {
                return response.text();
            }).then(function (text) {
                $("#inner").html(text)
            })
            // update progress counter
            $("#progress").text((current_index+1) + " / " + location_data.length);
            // enable buttons
            $(".tinder-btn").prop("disabled", false);
        } else {
            // no more locations -> display DONE button
            $("#progress").text("");
            $(".tinder-btn").prop("disabled", true);
            $("#inner").html("<button id=\"submit\" class=\"btn btn-success\">Download Data</button>");
            $("#submit").click(function () {
                fetch("/final", {
                    method: "POST",
                    headers: {"Content-Type": "application/json"},
                    body: JSON.stringify(location_data)
                }).then(function (response) {
                    return response.text();
                }).then(function (text) {
                    $("body").html(text)
                })
            })
        }
    }

</script>
{% endblock %}
{% block content %}
<div class="container">
    <h1>Solar-Tinder</h1>
    <!-- TODO future feature idea:
              * support swiping instead of yay/nay buttons
              * use alternate layout (willy's first design)
    -->
    <div class="row justify-content-md-center">
        <!-- content will be loaded in here -->
        <div class="col-md-auto">
            <div id="inner" class="card"></div>
        </div>

    </div>
    <!-- spacer row -->
    <div class="row">
        <div class="col">&nbsp;</div>
    </div>
    <div class="row justify-content-md-center">
        <div class="col-md-auto align-self-center">
            <p id="progress">0/0</p>
        </div>
    </div>
    <div class="row justify-content-md-center">
        <div class="col-md-auto align-self-center">
            <button id="nay" class="btn btn-primary align-middle tinder-btn">nay</button>
        </div>
        <div class="col-md-auto align-self-center">
            <button id="yay" class="btn btn-primary tinder-btn">yay</button>
        </div>
        <div class="col-md-auto align-self-center">
            <button id="premium" class="btn btn-primary tinder-btn">premium</button>
        </div>
        <div class="col-md-auto align-self-center">
            <button id="back" class="btn btn-danger" disabled>back</button>
        </div>
    </div>
</div>
{% endblock %}