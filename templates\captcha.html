{% extends "base.html" %}
{% block head %}
<script type="application/javascript">
    // TODO dynamically determine image size and count per page
    const IMAGES_PER_ROW = 20;
    const ROWS_PER_PAGE = 1;
    const IMAGES_PER_PAGE = IMAGES_PER_ROW * ROWS_PER_PAGE;
    location_data = {{ location_data | tojson | safe }};
    current_index = 0;
    $(document).ready(function () {
        // initialize all elements as deselected - this is required by the tinder endpoint later
        for(let i = 0; i < location_data.length; i++)
            location_data[i].selected = false
        console.log(location_data.length + " locations loaded");
        $(".btn-prev").click(function (){
            if(current_index >= IMAGES_PER_PAGE){
                current_index -= IMAGES_PER_PAGE;
                console.log("prev page! index is now " + current_index);
                draw_images();
                update_buttons();
            }else{
                console.log("prev button clicked at index " + current_index + "/" + location_data.length + " - that should not happen!");
            }
        });
        $(".btn-next").click(function (){
            if(current_index < location_data.length - IMAGES_PER_PAGE - 1){
                current_index += IMAGES_PER_PAGE;
                console.log("next page! index is now " + current_index);
                draw_images();
                update_buttons();
            }else{
                console.log("next button clicked at index " + current_index + "/" + location_data.length + " - that should not happen!");
            }
        });
        $(".btn-go").click(function () {
            var result = confirm("Zu Tinder wechseln?");
                if (result) {
                    fetch("/tinder", {
                        method: "POST",
                        headers: {"Content-Type": "application/json"},
                        body: JSON.stringify(location_data)
                    }).then(function (response){
                        return response.text();
                    }).then(function (text){
                        $("body").html(text);
                    })
                }
        });
        draw_images();
        update_buttons();
    });

    document.addEventListener('keydown', function(event) {
        if (event.keyCode === 37) {
            //alert('Left was pressed');
            $(".btn-prev").click();
        }
        else if (event.keyCode === 39) {
            //alert('Right was pressed');
            $(".btn-next").click();
        }
    }, true);

    function update_buttons(){
        $(".btn-next").prop("disabled", current_index + IMAGES_PER_PAGE >=  location_data.length);
        $(".btn-prev").prop("disabled", current_index < IMAGES_PER_PAGE);
        $(".badge-progress").text(
            (current_index + 1)
            + " - "
            + (Math.min(current_index + IMAGES_PER_PAGE, location_data.length))
            + " von "
            + location_data.length
        );
    }

    function draw_images() {
        let div = $("#captcha-content");
        div.html("");
        // add all rows to page, even if some of them will be empty
        for(let row_i = 0; row_i < ROWS_PER_PAGE; row_i++){
            div.append("<div class=\"row\" id='image-row-" + row_i + "'></div>");
        }
        for(let i = current_index; i < Math.min(current_index + IMAGES_PER_PAGE, location_data.length); i++){
            let data = location_data[i];
            let image_address = "/maps_image?lat=" + location_data[i].lat + "&lon=" + location_data[i].lon + "&marker=1";
            let selected = data.selected ? "location-selected " : "";
            // force int conversion to determine row index
            let row_i = Math.floor((i - current_index) / IMAGES_PER_ROW);
            $("#image-row-" + row_i).append(
                "<div class=\"col\">"
                + "<img class=\"location-image " + selected + "m-3\" id=\"image-" + i + "\" src=\"" + image_address + "\" height=\"250\" style=\"user-drag: none; -moz-user-select: none; -webkit-user-drag: none;\" alt=\"location image\">"
                + "</div>"
            );
        }
        $(".location-image").click(function () {
            $(this).toggleClass("location-selected");
            let clicked_idx = $(this).attr("id").split("-")[1];
            location_data[clicked_idx].selected = !location_data[clicked_idx].selected;
        });
    }

</script>
<style>
    .location-selected {
        border: 10px solid #0000ff;
    }
    .navbar {
      overflow: hidden;
      position: fixed;
      bottom: 0;
      display: flex;
        background-color: #AFAFAF;
    }

    .navbar > button{
        width: 80px;
        margin: 0 10px;
        padding: 10px;
    }

    .navbar > span{
        margin: 0 10px;
        padding: 17px;
    }

</style>
{% endblock %}
{% block content %}

<div class="container" style="margin:0px">
    <h1>Vorauswahl</h1>

    <!-- don't touch these buttons: they're updated by JS automatically -->
    <div class="navbar border">
        <button class="btn btn-primary btn-prev">Previous</button>
        <span class="badge bg-transparent badge-progress">PROGRESS</span>
        <button class="btn btn-primary btn-next">Next</button>
        <button class="btn btn-success btn-go">Go</button>
    </div>
    <div id="captcha-content">
        <!-- images will appear here -->
    </div>
</div>
{% endblock %}